# WSCCC Census System - Chatbot Module Architecture

## Overview

The WSCCC Census System's AI-powered analytics chatbot has been refactored into a professional, modular architecture that promotes maintainability, scalability, and code quality. This document provides a comprehensive guide to the new module structure.

## Architecture Principles

### 🎯 **Domain-Driven Design**
- Modules are organized by functional domain rather than technical layers
- Each module has a clear, single responsibility
- Clean separation of concerns across all modules

### 🔒 **Security-First Architecture**
- Dedicated security module with comprehensive protection measures
- Input validation, prompt injection detection, and secure error handling
- Rate limiting with memory leak prevention

### ⚡ **Performance Optimization**
- Request caching with LRU cache implementation
- Performance tracking and monitoring
- Optimized PostgreSQL queries with proper indexing

### 🌐 **Internationalization Support**
- Multilingual system prompts (English/Chinese)
- Locale-aware error messages and responses
- Cultural sensitivity in AI responses

## Module Structure

```
src/lib/analytics/chatbot/
├── 🤖 ai/                     # AI Operations Module
│   ├── intent-analysis.ts     # Google Gemini intent detection
│   ├── system-prompts.ts      # Multilingual prompt engineering  
│   ├── temperature.ts         # Dynamic temperature calculation
│   └── index.ts              # AI module exports
├── 🗄️ database/               # Database Operations Module
│   ├── intent-handlers.ts     # Intent-based query handlers
│   ├── temporal-intent-handlers.ts # Temporal analysis handlers
│   ├── distribution-queries.ts # Distribution table generators
│   ├── member-distributions.ts # Member-specific distributions
│   ├── household-distributions.ts # Household-specific distributions
│   ├── sacrament-distributions.ts # Sacrament-specific distributions
│   ├── keyword-queries.ts     # Keyword-based fallback queries
│   ├── keyword-query-executor.ts # Query execution with timeout
│   └── index.ts              # Database module exports
├── 🛡️ security/               # Security Module
│   ├── input-validation.ts    # Prompt injection detection
│   ├── error-handling.ts      # Secure error logging
│   ├── response-validation.ts # Response security filtering
│   ├── rate-limiting.ts       # LRU cache rate limiting
│   └── index.ts              # Security module exports
├── 🔧 utils/                  # Utilities Module
│   ├── cache.ts              # Request caching system
│   ├── analytics.ts          # Performance tracking
│   ├── helpers.ts            # Security and utility functions
│   └── index.ts              # Utils module exports
├── ⚙️ config.ts               # Centralized Configuration
└── 📋 validation.ts           # Zod schema validation
```

## Module Details

### 🤖 **AI Operations Module** (`src/lib/analytics/chatbot/ai/`)

**Purpose**: Handles all AI-related operations including intent analysis, prompt engineering, and temperature optimization.

**Key Components**:
- **Intent Analysis**: Google Gemini 2.5 Flash integration for query interpretation
- **System Prompts**: Multilingual prompt construction with security isolation
- **Temperature Control**: Dynamic temperature calculation based on message characteristics

**Example Usage**:
```typescript
import { 
  analyzeUserIntent, 
  buildSystemPromptWithContext,
  determineOptimalTemperature 
} from '@/lib/analytics/chatbot/ai';

// Analyze user intent with AI
const intent = await analyzeUserIntent(userMessage, request);

// Build context-aware system prompt
const systemPrompt = buildSystemPromptWithContext(databaseContext, 'English');

// Calculate optimal temperature
const temperature = determineOptimalTemperature(userMessage);
```

### 🗄️ **Database Operations Module** (`src/lib/analytics/chatbot/database/`)

**Purpose**: Manages all database interactions, query execution, and data processing.

**Key Components**:
- **Intent Handlers**: Process specific data types (demographics, households, sacraments)
- **Distribution Queries**: Generate statistical distribution tables
- **Keyword Queries**: Fallback system for low-confidence intents

**Example Usage**:
```typescript
import { 
  handleMemberDemographicsIntent,
  generateDistributionTable 
} from '@/lib/analytics/chatbot/database';

// Handle member demographics query
await handleMemberDemographicsIntent(intent, results);

// Generate distribution table
const tableData = await generateDistributionTable(intent);
```

### 🛡️ **Security Module** (`src/lib/analytics/chatbot/security/`)

**Purpose**: Provides comprehensive security measures including input validation, error handling, and rate limiting.

**Key Components**:
- **Input Validation**: Prompt injection detection and input sanitization
- **Error Handling**: Secure error logging with information leakage prevention
- **Rate Limiting**: LRU cache-based rate limiting with memory management

**Example Usage**:
```typescript
import { 
  detectPromptInjection,
  checkRateLimit,
  sanitizeUserInput 
} from '@/lib/analytics/chatbot/security';

// Security checks
if (detectPromptInjection(userMessage)) {
  return secureErrorResponse;
}

if (!checkRateLimit(userId)) {
  return rateLimitResponse;
}

const sanitizedInput = sanitizeUserInput(userMessage);
```

### 🔧 **Utilities Module** (`src/lib/analytics/chatbot/utils/`)

**Purpose**: Provides utility functions for caching, analytics, and helper operations.

**Key Components**:
- **Caching**: Request deduplication with TTL expiration
- **Analytics**: Performance tracking and monitoring
- **Helpers**: Security validation and utility functions

**Example Usage**:
```typescript
import { 
  getCachedResult,
  setCachedResult,
  createPerformanceTimer 
} from '@/lib/analytics/chatbot/utils';

// Check cache
const cachedResult = getCachedResult(userMessage);
if (cachedResult) return cachedResult;

// Track performance
const timer = createPerformanceTimer();
// ... process request ...
timer.trackAndGetElapsed();

// Cache result
setCachedResult(userMessage, result);
```

## Configuration Management

### ⚙️ **Centralized Configuration** (`src/lib/analytics/chatbot/config.ts`)

All configuration values are centralized for easy management:

```typescript
// API Configuration
export const GEMINI_API_KEY = process.env.GOOGLE_GEMINI_API_KEY;
export const GEMINI_MODEL_NAME = process.env.GEMINI_MODEL_NAME || 'gemini-2.5-flash-preview-05-20';

// Performance Configuration
export const REQUEST_TIMEOUT = 45_000; // 45 seconds
export const MAX_QUERY_LENGTH = 500;

// Feature Flags
export const ENABLE_DEBUG_LOGGING = process.env.NODE_ENV === 'development';
export const ENABLE_CHART_GENERATION = true;

// Security Configuration
export const RATE_LIMIT_MAX_REQUESTS = 20; // per minute per user
export const CACHE_DURATION = 30_000; // 30 seconds
```

## Migration Benefits

### 📈 **Code Quality Improvements**
- **84.5% Code Reduction**: Main route file reduced from 3,495 lines to 542 lines
- **Professional Naming**: Eliminated numbered file suffixes (distribution-queries-2.ts)
- **Clean Architecture**: Domain-based module organization
- **Type Safety**: Comprehensive TypeScript integration

### 🚀 **Performance Benefits**
- **Faster Development**: Easier to locate and modify specific functionality
- **Better Testing**: Isolated modules enable focused unit testing
- **Improved Maintainability**: Clear separation of concerns
- **Enhanced Scalability**: Easy to add new features without affecting existing code

### 🔒 **Security Enhancements**
- **Centralized Security**: All security measures in dedicated module
- **Consistent Validation**: Standardized input validation across all entry points
- **Secure Error Handling**: Prevents information leakage in production

## Best Practices

### 🎯 **Module Usage Guidelines**
1. **Import from Index Files**: Always use module index exports for clean imports
2. **Respect Module Boundaries**: Don't import directly from internal module files
3. **Use Configuration Module**: Access all config values through centralized config
4. **Follow Security Patterns**: Always validate inputs and sanitize outputs

### 📝 **Development Workflow**
1. **Understand Module Purpose**: Each module has a specific domain responsibility
2. **Check Existing Functions**: Avoid duplicating functionality across modules
3. **Use TypeScript**: Leverage type safety for better development experience
4. **Test Module Isolation**: Ensure modules can be tested independently

## Related Documentation

- **[API Documentation](./API.md)** - Complete API reference with updated endpoints
- **[Security Guide](./SECURITY.md)** - Security implementation details
- **[AI Analytics Guide](./AI-ANALYTICS.md)** - AI chatbot usage and configuration

This modular architecture ensures the WSCCC Census System's chatbot remains maintainable, scalable, and secure for future development.
